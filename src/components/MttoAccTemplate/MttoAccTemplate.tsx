import { FC, useState } from "react";
import Table from "../MetlifeComponents/Table/Table";
import { mockColumns, mockData } from "./mttoAccTemplateMockData";
import { TableData } from "../MetlifeComponents/Table/table-types";
import Modal from "../Modal/Modal";
import Button from "../MetlifeComponents/Button/Button";

type MttoAccTemplateProps = {};

const MttoAccTemplate: FC<MttoAccTemplateProps> = () => {
  const [data, setData] = useState<TableData[]>(mockData);

  const handleSave = (element: TableData) => {
    console.log("Guardando elemento:", element);

    // Generate new ID
    const newId = (data.length + 1).toString();

    // Create complete element with default values for non-required fields
    const newElement: TableData = {
      id: newId,
      company: element.company || "",
      area: element.area || "",
      module: element.module || "",
      concept: element.concept || "",
      templateId: element.templateId || 0,
      description: element.description || "",
      movementType: element.movementType || "",
      // Default values for non-required fields
      accountingEntry: 0,
      account: 0,
      subAccount: 0,
      subSubAccount: 0,
      auxiliar1: 0,
      auxiliar2: 0,
      sixthLevel: 0,
      deleteActionExtraProps: {
        "aria-label": "Borrar",
        "data-tooltip": "Borrar",
      },
      editActionExtraProps: {
        id: `edit-action-${newId}`,
        "aria-label": "Editar",
        "data-tooltip": "Editar",
      },
    };

    // Update state with new element
    setData([newElement, ...data]);

    console.log("Elemento agregado exitosamente:", newElement);
  };

  const handleEdit = (element: TableData) => {
    console.log("Editando elemento:", element);
    // Aquí se implementaría la lógica para editar el elemento
  };

  const handleDelete = (elementId: number) => {
    console.log("Eliminando elemento con ID:", elementId);
    // Aquí se implementaría la lógica para eliminar el elemento
  };

  return (
    <div className="container">
      <h1 className="title">MANTENIMIENTO DE PLANTILLAS CONTABLES</h1>
      <Table
        id="accTemplates"
        data={data}
        columns={mockColumns}
        filters={[
          "company",
          "area",
          "description",
          "concept",
          "movementType",
          "module",
          "templateId",
        ]}
        onSave={handleSave}
        onEdit={handleEdit}
        onDelete={handleDelete}
        enableAddRow={true}
      />
    </div>
  );
};

export default MttoAccTemplate;
